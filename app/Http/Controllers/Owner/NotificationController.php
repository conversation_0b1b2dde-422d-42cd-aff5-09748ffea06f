<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\OwnerNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class NotificationController extends Controller
{
    /**
     * Display a listing of notifications.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();
        
        // Get filter parameters
        $filters = $request->only([
            'type', 'priority', 'status', 'date_from', 'date_to', 'search'
        ]);

        // Build query with enterprise isolation
        $query = OwnerNotification::where('business_id', $business->id)
                                 ->where('owner_id', auth()->id());

        // Apply filters
        $this->applyFilters($query, $filters);

        // Get notifications with pagination
        $notifications = $query->orderBy('created_at', 'desc')
                              ->paginate(20)
                              ->withQueryString();

        // Get statistics
        $stats = $this->getNotificationStats($business);

        return view('owner.notifications.index', compact(
            'notifications', 'stats', 'filters'
        ));
    }

    /**
     * Get notification statistics.
     */
    public function getStats(Request $request)
    {
        $business = $this->getUserBusiness();
        $stats = $this->getNotificationStats($business);

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Show the specified notification.
     */
    public function show(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        
        $notification = OwnerNotification::where('business_id', $business->id)
                                        ->where('owner_id', auth()->id())
                                        ->findOrFail($id);

        // Mark as read if not already read
        if (!$notification->is_read) {
            $notification->markAsRead();
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'notification' => $notification->load(['business', 'owner'])
            ]);
        }

        return view('owner.notifications.show', compact('notification'));
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        
        $notification = OwnerNotification::where('business_id', $business->id)
                                        ->where('owner_id', auth()->id())
                                        ->findOrFail($id);

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }

    /**
     * Mark notification as unread.
     */
    public function markAsUnread(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        
        $notification = OwnerNotification::where('business_id', $business->id)
                                        ->where('owner_id', auth()->id())
                                        ->findOrFail($id);

        $notification->markAsUnread();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as unread'
        ]);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $count = OwnerNotification::where('business_id', $business->id)
                                 ->where('owner_id', auth()->id())
                                 ->where('is_read', false)
                                 ->where('is_deleted', false)
                                 ->update([
                                     'is_read' => true,
                                     'read_at' => now()
                                 ]);

        return response()->json([
            'success' => true,
            'message' => "Marked {$count} notifications as read"
        ]);
    }

    /**
     * Soft delete notification.
     */
    public function delete(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        
        $notification = OwnerNotification::where('business_id', $business->id)
                                        ->where('owner_id', auth()->id())
                                        ->findOrFail($id);

        $notification->softDelete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully'
        ]);
    }

    /**
     * Restore deleted notification.
     */
    public function restore(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        
        $notification = OwnerNotification::where('business_id', $business->id)
                                        ->where('owner_id', auth()->id())
                                        ->findOrFail($id);

        $notification->restore();

        return response()->json([
            'success' => true,
            'message' => 'Notification restored successfully'
        ]);
    }

    /**
     * Permanently delete notification.
     */
    public function destroy(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        
        $notification = OwnerNotification::where('business_id', $business->id)
                                        ->where('owner_id', auth()->id())
                                        ->findOrFail($id);

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification permanently deleted'
        ]);
    }

    /**
     * Bulk operations on notifications.
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:mark_read,mark_unread,delete,restore,destroy',
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'integer|exists:owner_notifications,id'
        ]);

        $business = $this->getUserBusiness();
        $action = $request->action;
        $notificationIds = $request->notification_ids;

        // Ensure all notifications belong to the authenticated owner's business
        $notifications = OwnerNotification::where('business_id', $business->id)
                                         ->where('owner_id', auth()->id())
                                         ->whereIn('id', $notificationIds)
                                         ->get();

        if ($notifications->count() !== count($notificationIds)) {
            return response()->json([
                'success' => false,
                'message' => 'Some notifications could not be found or do not belong to your business'
            ], 403);
        }

        $count = 0;
        foreach ($notifications as $notification) {
            switch ($action) {
                case 'mark_read':
                    if (!$notification->is_read) {
                        $notification->markAsRead();
                        $count++;
                    }
                    break;
                case 'mark_unread':
                    if ($notification->is_read) {
                        $notification->markAsUnread();
                        $count++;
                    }
                    break;
                case 'delete':
                    if (!$notification->is_deleted) {
                        $notification->softDelete();
                        $count++;
                    }
                    break;
                case 'restore':
                    if ($notification->is_deleted) {
                        $notification->restore();
                        $count++;
                    }
                    break;
                case 'destroy':
                    $notification->delete();
                    $count++;
                    break;
            }
        }

        $actionMessages = [
            'mark_read' => 'marked as read',
            'mark_unread' => 'marked as unread',
            'delete' => 'deleted',
            'restore' => 'restored',
            'destroy' => 'permanently deleted'
        ];

        return response()->json([
            'success' => true,
            'message' => "{$count} notifications {$actionMessages[$action]} successfully"
        ]);
    }

    /**
     * Get unread notification count.
     */
    public function getUnreadCount(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $count = OwnerNotification::where('business_id', $business->id)
                                 ->where('owner_id', auth()->id())
                                 ->unread()
                                 ->notDeleted()
                                 ->count();

        return response()->json([
            'success' => true,
            'count' => $count
        ]);
    }

    /**
     * Get recent notifications for real-time updates.
     */
    public function getRecent(Request $request)
    {
        $business = $this->getUserBusiness();
        $since = $request->get('since', now()->subMinutes(5));
        
        $notifications = OwnerNotification::where('business_id', $business->id)
                                         ->where('owner_id', auth()->id())
                                         ->where('created_at', '>', $since)
                                         ->notDeleted()
                                         ->orderBy('created_at', 'desc')
                                         ->limit(10)
                                         ->get();

        return response()->json([
            'success' => true,
            'notifications' => $notifications
        ]);
    }

    /**
     * Apply filters to the notification query.
     */
    private function applyFilters($query, $filters)
    {
        if (!empty($filters['type'])) {
            $query->where('notification_type', $filters['type']);
        }

        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        if (!empty($filters['status'])) {
            switch ($filters['status']) {
                case 'unread':
                    $query->where('is_read', false);
                    break;
                case 'read':
                    $query->where('is_read', true);
                    break;
                case 'deleted':
                    $query->where('is_deleted', true);
                    break;
                default:
                    $query->where('is_deleted', false);
                    break;
            }
        } else {
            // Default to non-deleted notifications
            $query->where('is_deleted', false);
        }

        if (!empty($filters['date_from'])) {
            $query->whereDate('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->whereDate('created_at', '<=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }
    }

    /**
     * Get notification statistics for the business.
     */
    private function getNotificationStats($business)
    {
        $baseQuery = OwnerNotification::where('business_id', $business->id)
                                     ->where('owner_id', auth()->id());

        return [
            'total' => (clone $baseQuery)->notDeleted()->count(),
            'unread' => (clone $baseQuery)->unread()->notDeleted()->count(),
            'urgent' => (clone $baseQuery)->urgent()->notDeleted()->count(),
            'today' => (clone $baseQuery)->whereDate('created_at', today())->notDeleted()->count(),
            'this_week' => (clone $baseQuery)->where('created_at', '>=', now()->startOfWeek())->notDeleted()->count(),
            'by_type' => (clone $baseQuery)->notDeleted()
                                          ->select('notification_type', DB::raw('count(*) as count'))
                                          ->groupBy('notification_type')
                                          ->pluck('count', 'notification_type')
                                          ->toArray(),
            'by_priority' => (clone $baseQuery)->notDeleted()
                                              ->select('priority', DB::raw('count(*) as count'))
                                              ->groupBy('priority')
                                              ->pluck('count', 'priority')
                                              ->toArray(),
        ];
    }
}
