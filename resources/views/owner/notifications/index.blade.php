@extends('owner.layouts.app')

@section('title', 'Notifications')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Notifications</h1>
            <p class="text-muted">Manage your business notifications and alerts</p>
        </div>
        <div class="d-flex" style="gap: 10px;">
            <button type="button" class="btn btn-outline-primary" id="markAllReadBtn">
                <i class="fas fa-check-double"></i> Mark All Read
            </button>
            <button type="button" class="btn btn-outline-secondary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button type="button" class="btn btn-outline-info" id="testBtn">
                <i class="fas fa-bug"></i> Test
            </button>
        </div>
    </div>
@stop

@push('css')
<style>
.notification-row.unread {
    background-color: #fff3cd !important;
}
.notification-row.read {
    background-color: #ffffff;
}
.notification-actions .btn {
    margin-right: 5px;
}
.notification-priority-urgent {
    border-left: 4px solid #dc3545;
}
.notification-priority-high {
    border-left: 4px solid #ffc107;
}
</style>
@endpush

@section('content')
    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="totalCount">{{ $stats['total'] ?? 0 }}</h3>
                    <p>Total Notifications</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="unreadCount">{{ $stats['unread'] ?? 0 }}</h3>
                    <p>Unread</p>
                </div>
                <div class="icon">
                    <i class="fas fa-envelope"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="urgentCount">{{ $stats['urgent'] ?? 0 }}</h3>
                    <p>Urgent</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="todayCount">{{ $stats['today'] ?? 0 }}</h3>
                    <p>Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Filters and Search --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters & Search
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('owner.notifications.index') }}" id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="type">Type</label>
                            <select name="type" id="type" class="form-control">
                                <option value="">All Types</option>
                                <option value="booking" {{ request('type') === 'booking' ? 'selected' : '' }}>Bookings</option>
                                <option value="cancellation" {{ request('type') === 'cancellation' ? 'selected' : '' }}>Cancellations</option>
                                <option value="payment" {{ request('type') === 'payment' ? 'selected' : '' }}>Payments</option>
                                <option value="review" {{ request('type') === 'review' ? 'selected' : '' }}>Reviews</option>
                                <option value="system" {{ request('type') === 'system' ? 'selected' : '' }}>System</option>
                                <option value="marketing" {{ request('type') === 'marketing' ? 'selected' : '' }}>Marketing</option>
                                <option value="alert" {{ request('type') === 'alert' ? 'selected' : '' }}>Alerts</option>
                                <option value="reminder" {{ request('type') === 'reminder' ? 'selected' : '' }}>Reminders</option>
                                <option value="customer_message" {{ request('type') === 'customer_message' ? 'selected' : '' }}>Customer Messages</option>
                                <option value="waiting_list" {{ request('type') === 'waiting_list' ? 'selected' : '' }}>Waiting Lists</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="priority">Priority</label>
                            <select name="priority" id="priority" class="form-control">
                                <option value="">All Priorities</option>
                                <option value="urgent" {{ request('priority') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                <option value="high" {{ request('priority') === 'high' ? 'selected' : '' }}>High</option>
                                <option value="normal" {{ request('priority') === 'normal' ? 'selected' : '' }}>Normal</option>
                                <option value="low" {{ request('priority') === 'low' ? 'selected' : '' }}>Low</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All</option>
                                <option value="unread" {{ request('status') === 'unread' ? 'selected' : '' }}>Unread</option>
                                <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>Read</option>
                                <option value="deleted" {{ request('status') === 'deleted' ? 'selected' : '' }}>Deleted</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" placeholder="Search notifications..." value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-secondary" id="clearFiltersBtn">
                                    <i class="fas fa-times"></i> Clear Filters
                                </button>
                                <button type="button" class="btn btn-info" id="bulkActionsBtn" disabled>
                                    <i class="fas fa-tasks"></i> Bulk Actions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Notifications List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-2"></i>
                Notifications
                @if($notifications->total() > 0)
                    <span class="badge badge-info ml-2">{{ $notifications->total() }}</span>
                @endif
            </h3>
            <div class="card-tools">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll">
                    <label class="form-check-label" for="selectAll">
                        Select All
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($notifications->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <tbody>
                            @foreach($notifications as $notification)
                                <tr class="notification-row {{ !$notification->is_read ? 'unread table-warning' : 'read' }} {{ $notification->priority === 'urgent' ? 'notification-priority-urgent' : '' }} {{ $notification->priority === 'high' ? 'notification-priority-high' : '' }}"
                                    data-id="{{ $notification->id }}"
                                    data-read="{{ $notification->is_read ? 'true' : 'false' }}">
                                    <td width="30">
                                        <div class="form-check">
                                            <input class="form-check-input notification-checkbox"
                                                   type="checkbox"
                                                   value="{{ $notification->id }}"
                                                   id="notification_{{ $notification->id }}">
                                        </div>
                                    </td>
                                    <td width="50">
                                        <div class="d-flex align-items-center">
                                            <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }} mr-2"></i>
                                            @if($notification->priority === 'urgent' || $notification->priority === 'high')
                                                <i class="{{ $notification->priority_icon }} text-{{ $notification->priority_color }}"></i>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 notification-title {{ !$notification->is_read ? 'font-weight-bold' : '' }}">
                                                    {{ $notification->title }}
                                                    @if(!$notification->is_read)
                                                        <span class="badge badge-primary badge-sm ml-1 new-badge">New</span>
                                                    @endif
                                                    @if($notification->priority === 'urgent')
                                                        <span class="badge badge-danger badge-sm ml-1">Urgent</span>
                                                    @elseif($notification->priority === 'high')
                                                        <span class="badge badge-warning badge-sm ml-1">High</span>
                                                    @endif
                                                </h6>
                                                <p class="mb-1 text-muted">{{ $notification->short_message }}</p>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    {{ $notification->formatted_created_at }}
                                                    @if($notification->source_type)
                                                        • <span class="text-capitalize">{{ str_replace('_', ' ', $notification->source_type) }}</span>
                                                    @endif
                                                </small>
                                            </div>
                                            <div class="ml-3 notification-actions">
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button"
                                                            class="btn btn-outline-primary btn-sm view-notification"
                                                            data-id="{{ $notification->id }}"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    @if(!$notification->is_read)
                                                        <button type="button"
                                                                class="btn btn-outline-success btn-sm mark-read"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Read"
                                                                style="display: inline-block;">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button"
                                                                class="btn btn-outline-warning btn-sm mark-unread"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Unread"
                                                                style="display: none;">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    @else
                                                        <button type="button"
                                                                class="btn btn-outline-success btn-sm mark-read"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Read"
                                                                style="display: none;">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button"
                                                                class="btn btn-outline-warning btn-sm mark-unread"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Unread"
                                                                style="display: inline-block;">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    @endif
                                                    <button type="button"
                                                            class="btn btn-outline-danger btn-sm delete-notification"
                                                            data-id="{{ $notification->id }}"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4>No Notifications Found</h4>
                    <p class="text-muted">
                        @if(request()->hasAny(['type', 'priority', 'status', 'search', 'date_from', 'date_to']))
                            No notifications match your current filters. Try adjusting your search criteria.
                        @else
                            You don't have any notifications yet. They will appear here when you receive them.
                        @endif
                    </p>
                    @if(request()->hasAny(['type', 'priority', 'status', 'search', 'date_from', 'date_to']))
                        <a href="{{ route('owner.notifications.index') }}" class="btn btn-primary">
                            <i class="fas fa-times mr-1"></i> Clear Filters
                        </a>
                    @endif
                </div>
            @endif
        </div>
        @if($notifications->hasPages())
            <div class="card-footer">
                {{ $notifications->links() }}
            </div>
        @endif
    </div>

    {{-- Notification Detail Modal --}}
    <div class="modal fade" id="notificationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-bell mr-2"></i>
                        Notification Details
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="notificationModalBody">
                    <div class="text-center py-3">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Loading notification details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="markReadFromModal" style="display: none;">
                        <i class="fas fa-check"></i> Mark as Read
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Bulk Actions Modal --}}
    <div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-tasks mr-2"></i>
                        Bulk Actions
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Select an action to perform on <span id="selectedCount">0</span> selected notifications:</p>
                    <div class="list-group">
                        <button type="button" class="list-group-item list-group-item-action bulk-action" data-action="mark_read">
                            <i class="fas fa-check text-success mr-2"></i>
                            Mark as Read
                        </button>
                        <button type="button" class="list-group-item list-group-item-action bulk-action" data-action="mark_unread">
                            <i class="fas fa-undo text-warning mr-2"></i>
                            Mark as Unread
                        </button>
                        <button type="button" class="list-group-item list-group-item-action bulk-action" data-action="delete">
                            <i class="fas fa-trash text-danger mr-2"></i>
                            Delete Selected
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // CSRF token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Toastr is now loaded from the main layout

    // Auto-refresh notifications every 30 seconds
    let autoRefreshInterval = setInterval(function() {
        refreshStats();
    }, 30000);

    // Refresh button
    $('#refreshBtn').click(function() {
        console.log('Refresh button clicked');
        location.reload();
    });

    // Clear filters button
    $('#clearFiltersBtn').click(function() {
        console.log('Clear filters button clicked');
        window.location.href = '{{ route("owner.notifications.index") }}';
    });

    // Test button
    $('#testBtn').click(function() {
        console.log('Test button clicked');
        toastr.success('JavaScript is working correctly!');
        console.log('jQuery version:', $.fn.jquery);
        console.log('Toastr available:', typeof toastr !== 'undefined');
        console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));
    });

    // Select all checkbox
    $('#selectAll').change(function() {
        $('.notification-checkbox').prop('checked', this.checked);
        updateBulkActionsButton();
    });

    // Individual notification checkboxes
    $(document).on('change', '.notification-checkbox', function() {
        updateBulkActionsButton();

        // Update select all checkbox
        const totalCheckboxes = $('.notification-checkbox').length;
        const checkedCheckboxes = $('.notification-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update bulk actions button state
    function updateBulkActionsButton() {
        const checkedCount = $('.notification-checkbox:checked').length;
        $('#bulkActionsBtn').prop('disabled', checkedCount === 0);
        $('#selectedCount').text(checkedCount);
    }

    // Bulk actions button
    $('#bulkActionsBtn').click(function() {
        const checkedCount = $('.notification-checkbox:checked').length;
        if (checkedCount > 0) {
            $('#selectedCount').text(checkedCount);
            $('#bulkActionsModal').modal('show');
        }
    });

    // Bulk action execution
    $('.bulk-action').click(function() {
        const action = $(this).data('action');
        const selectedIds = $('.notification-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            toastr.warning('No notifications selected');
            return;
        }

        // Confirm destructive actions
        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected notifications?')) {
                return;
            }
        }

        performBulkAction(action, selectedIds);
        $('#bulkActionsModal').modal('hide');
    });

    // Mark all as read
    $('#markAllReadBtn').click(function() {
        console.log('Mark all read button clicked');
        if (confirm('Mark all notifications as read?')) {
            console.log('Sending mark all read request');
            $.post('{{ route("owner.notifications.mark-all-read") }}')
                .done(function(response) {
                    console.log('Mark all read response:', response);
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error('Failed to mark notifications as read');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Mark all read failed:', xhr, status, error);
                    toastr.error('Failed to mark notifications as read');
                });
        }
    });

    // View notification details
    $(document).on('click', '.view-notification', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('View notification clicked:', notificationId);
        loadNotificationDetails(notificationId);
    });

    // Mark as read
    $(document).on('click', '.mark-read', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('Mark as read clicked:', notificationId);
        markNotificationAsRead(notificationId);
    });

    // Mark as unread
    $(document).on('click', '.mark-unread', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('Mark as unread clicked:', notificationId);
        markNotificationAsUnread(notificationId);
    });

    // Delete notification
    $(document).on('click', '.delete-notification', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('Delete notification clicked:', notificationId);
        if (confirm('Are you sure you want to delete this notification?')) {
            deleteNotification(notificationId);
        }
    });

    // Mark as read from modal
    $('#markReadFromModal').click(function() {
        const notificationId = $(this).data('id');
        markNotificationAsRead(notificationId);
        $('#notificationModal').modal('hide');
    });

    // Functions
    function refreshStats() {
        $.get('{{ route("owner.notifications.stats") }}')
            .done(function(response) {
                if (response.success) {
                    $('#totalCount').text(response.stats.total);
                    $('#unreadCount').text(response.stats.unread);
                    $('#urgentCount').text(response.stats.urgent);
                    $('#todayCount').text(response.stats.today);
                }
            });
    }

    function loadNotificationDetails(notificationId) {
        $('#notificationModal').modal('show');
        $('#notificationModalBody').html(`
            <div class="text-center py-3">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading notification details...</p>
            </div>
        `);

        $.get(`{{ route("owner.notifications.show", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                if (response.success) {
                    const notification = response.notification;
                    const isRead = notification.is_read;

                    $('#notificationModalBody').html(`
                        <div class="notification-detail">
                            <div class="d-flex align-items-center mb-3">
                                <i class="${getTypeIcon(notification.notification_type)} text-${getTypeColor(notification.notification_type)} fa-2x mr-3"></i>
                                <div>
                                    <h5 class="mb-1">${notification.title}</h5>
                                    <small class="text-muted">
                                        <i class="fas fa-clock mr-1"></i>
                                        ${new Date(notification.created_at).toLocaleString()}
                                        ${notification.priority !== 'normal' ? `• <span class="badge badge-${getPriorityColor(notification.priority)}">${notification.priority.toUpperCase()}</span>` : ''}
                                    </small>
                                </div>
                            </div>
                            <div class="notification-message">
                                <p>${notification.message}</p>
                            </div>
                            ${notification.data && Object.keys(notification.data).length > 0 ? `
                                <div class="notification-data mt-3">
                                    <h6>Additional Details:</h6>
                                    <div class="bg-light p-3 rounded">
                                        ${formatNotificationData(notification.data)}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `);

                    // Show/hide mark as read button
                    if (!isRead) {
                        $('#markReadFromModal').show().data('id', notificationId);
                    } else {
                        $('#markReadFromModal').hide();
                    }
                } else {
                    $('#notificationModalBody').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Failed to load notification details.
                        </div>
                    `);
                }
            })
            .fail(function() {
                $('#notificationModalBody').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Failed to load notification details.
                    </div>
                `);
            });
    }

    function markNotificationAsRead(notificationId) {
        console.log('Marking notification as read:', notificationId);
        $.post(`{{ route("owner.notifications.mark-read", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                console.log('Mark as read response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    updateNotificationRow(notificationId, true);
                    refreshStats();
                } else {
                    toastr.error('Failed to mark notification as read');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Mark as read failed:', xhr, status, error);
                toastr.error('Failed to mark notification as read: ' + (xhr.responseJSON?.message || error));
            });
    }

    function markNotificationAsUnread(notificationId) {
        console.log('Marking notification as unread:', notificationId);
        $.post(`{{ route("owner.notifications.mark-unread", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                console.log('Mark as unread response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    updateNotificationRow(notificationId, false);
                    refreshStats();
                } else {
                    toastr.error('Failed to mark notification as unread');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Mark as unread failed:', xhr, status, error);
                toastr.error('Failed to mark notification as unread: ' + (xhr.responseJSON?.message || error));
            });
    }

    function deleteNotification(notificationId) {
        console.log('Deleting notification:', notificationId);
        $.post(`{{ route("owner.notifications.delete", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                console.log('Delete response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    $(`tr[data-id="${notificationId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });
                    refreshStats();
                } else {
                    toastr.error('Failed to delete notification');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Delete failed:', xhr, status, error);
                toastr.error('Failed to delete notification: ' + (xhr.responseJSON?.message || error));
            });
    }

    function performBulkAction(action, notificationIds) {
        $.post('{{ route("owner.notifications.bulk-action") }}', {
            action: action,
            notification_ids: notificationIds
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);

                // Update UI based on action
                if (action === 'delete') {
                    notificationIds.forEach(function(id) {
                        $(`tr[data-id="${id}"]`).fadeOut(300, function() {
                            $(this).remove();
                        });
                    });
                } else if (action === 'mark_read') {
                    notificationIds.forEach(function(id) {
                        updateNotificationRow(id, true);
                    });
                } else if (action === 'mark_unread') {
                    notificationIds.forEach(function(id) {
                        updateNotificationRow(id, false);
                    });
                }

                // Clear selections
                $('.notification-checkbox').prop('checked', false);
                $('#selectAll').prop('checked', false);
                updateBulkActionsButton();
                refreshStats();
            } else {
                toastr.error('Failed to perform bulk action');
            }
        })
        .fail(function() {
            toastr.error('Failed to perform bulk action');
        });
    }

    function updateNotificationRow(notificationId, isRead) {
        const row = $(`tr[data-id="${notificationId}"]`);
        const readButton = row.find('.mark-read');
        const unreadButton = row.find('.mark-unread');
        const title = row.find('h6');
        const newBadge = row.find('.badge-primary');

        if (isRead) {
            row.removeClass('table-warning');
            row.data('read', 'true');
            title.removeClass('font-weight-bold');
            newBadge.remove();
            readButton.hide();
            unreadButton.show().removeClass('btn-outline-warning').addClass('btn-outline-warning');
        } else {
            row.addClass('table-warning');
            row.data('read', 'false');
            title.addClass('font-weight-bold');
            if (newBadge.length === 0) {
                title.append('<span class="badge badge-primary badge-sm ml-1">New</span>');
            }
            unreadButton.hide();
            readButton.show().removeClass('btn-outline-success').addClass('btn-outline-success');
        }
    }

    // Helper functions for notification display
    function getTypeIcon(type) {
        const icons = {
            'booking': 'fas fa-calendar-check',
            'cancellation': 'fas fa-calendar-times',
            'payment': 'fas fa-credit-card',
            'review': 'fas fa-star',
            'system': 'fas fa-cog',
            'marketing': 'fas fa-bullhorn',
            'alert': 'fas fa-exclamation-triangle',
            'reminder': 'fas fa-clock',
            'customer_message': 'fas fa-comment',
            'waiting_list': 'fas fa-list'
        };
        return icons[type] || 'fas fa-bell';
    }

    function getTypeColor(type) {
        const colors = {
            'booking': 'success',
            'cancellation': 'danger',
            'payment': 'info',
            'review': 'warning',
            'system': 'secondary',
            'marketing': 'primary',
            'alert': 'danger',
            'reminder': 'warning',
            'customer_message': 'info',
            'waiting_list': 'primary'
        };
        return colors[type] || 'info';
    }

    function getPriorityColor(priority) {
        const colors = {
            'urgent': 'danger',
            'high': 'warning',
            'normal': 'info',
            'low': 'secondary'
        };
        return colors[priority] || 'info';
    }

    function formatNotificationData(data) {
        let html = '';
        for (const [key, value] of Object.entries(data)) {
            if (value !== null && value !== undefined && value !== '') {
                const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                let formattedValue = value;

                // Format specific data types
                if (key.includes('date') || key.includes('time')) {
                    formattedValue = new Date(value).toLocaleString();
                } else if (key.includes('amount') || key.includes('value')) {
                    formattedValue = `$${parseFloat(value).toFixed(2)}`;
                } else if (typeof value === 'object') {
                    formattedValue = JSON.stringify(value, null, 2);
                }

                html += `<div class="row mb-1">
                    <div class="col-4"><strong>${formattedKey}:</strong></div>
                    <div class="col-8">${formattedValue}</div>
                </div>`;
            }
        }
        return html || '<p class="text-muted">No additional details available.</p>';
    }

    // Real-time search
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#filterForm').submit();
        }, 500);
    });

    // Auto-submit filters on change
    $('#type, #priority, #status, #date_from, #date_to').change(function() {
        $('#filterForm').submit();
    });
});
</script>
@stop